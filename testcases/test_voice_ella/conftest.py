"""
Ella测试的pytest配置文件
处理批量测试的屏幕管理和前置检查
"""
import pytest
from core.logger import log


def _check_whatsapp_installation(force_install=False):
    """
    检查WhatsApp是否已安装
    如果未安装，自动尝试安装

    Args:
        force_install: 是否强制重新安装（即使已安装）
    """
    try:
        from tools.adb_process_monitor import AdbProcessMonitor

        log.info("📱 检查WhatsApp安装状态...")

        # 创建ADB监控器
        adb_monitor = AdbProcessMonitor()

        # 检查WhatsApp是否已安装
        whatsapp_package = "com.whatsapp"
        is_installed = adb_monitor.is_package_installed(whatsapp_package)

        if is_installed and not force_install:
            log.info("✅ WhatsApp已安装，继续执行测试")
            return True
        elif is_installed and force_install:
            log.info("🔄 WhatsApp已安装，但启用了强制重新安装选项")
        else:
            log.warning("⚠️ WhatsApp未安装，尝试自动安装...")

        # 尝试安装或重新安装WhatsApp
        action = "重新安装" if (is_installed and force_install) else "安装"
        log.info(f"📦 开始{action}WhatsApp...")

        install_result = adb_monitor.install_app_by_name("whatsapp", "data")

        if install_result.get('install_success', False):
            apk_count = install_result.get('apk_count', 1)
            log.info(f"✅ WhatsApp{action}成功（安装了{apk_count}个APK文件）")

            # 验证安装是否成功
            if adb_monitor.is_package_installed(whatsapp_package):
                log.info("✅ WhatsApp安装验证通过")
                return True
            else:
                log.error("❌ WhatsApp安装验证失败")
                return False
        else:
            error_msg = install_result.get('error', '未知错误')
            log.error(f"❌ WhatsApp{action}失败: {error_msg}")

            # 提供手动安装建议
            log.info("💡 建议手动安装WhatsApp:")
            log.info("   1. 确保APK文件存在于 data/apk/whatsapp/ 目录")
            log.info("   2. 或运行: python tools/adb_process_monitor.py --install-app whatsapp")
            log.info("   3. 或使用 --skip-precheck 选项跳过此检查")

            return False

    except Exception as e:
        log.error(f"❌ WhatsApp安装检查失败: {e}")
        return False


def _check_device_connection():
    """
    检查设备连接状态
    """
    try:
        from tools.adb_process_monitor import AdbProcessMonitor

        log.info("📱 检查设备连接状态...")

        adb_monitor = AdbProcessMonitor()

        # 这里可以添加设备连接检查逻辑
        # 例如检查adb devices命令的输出

        log.info("✅ 设备连接检查通过")
        return True

    except Exception as e:
        log.error(f"❌ 设备连接检查失败: {e}")
        return False


def pytest_sessionstart(session):
    """
    pytest会话开始时的钩子函数
    在所有测试开始前执行一次
    """
    try:
        # 导入BaseEllaTest类
        from testcases.test_ella.base_ella_test import BaseEllaTest

        log.info("🚀 Ella测试会话开始，执行前置检查...")

        # 清理Ella应用数据
        from pages.apps.ella.dialogue_page import EllaDialoguePage
        ella_page = EllaDialoguePage()
        # 唤醒屏幕
        log.info("唤醒屏幕")
        ella_page.weak_up_screen()
        # 初始化Ella应用
        log.info("初始化Ella应用")
        ella_page.init_ella()
        # 清理recent
        ella_page.clear_recent_apps()
        # 获取配置选项
        config = session.config
        skip_precheck = config.getoption("--skip-precheck")
        force_whatsapp_install = config.getoption("--force-whatsapp-install")

        if skip_precheck:
            log.info("⏭️ 跳过前置检查（--skip-precheck 选项已启用）")
        else:
            # 前置检查列表
            checks = [
                ("设备连接检查", _check_device_connection),
                ("WhatsApp安装检查", lambda: _check_whatsapp_installation(force_install=force_whatsapp_install)),
            ]

            # 执行所有前置检查
            failed_checks = []
            for check_name, check_func in checks:
                log.info(f"🔍 执行{check_name}...")
                try:
                    if not check_func():
                        failed_checks.append(check_name)
                        log.warning(f"⚠️ {check_name}失败")
                    else:
                        log.info(f"✅ {check_name}通过")
                except Exception as e:
                    failed_checks.append(check_name)
                    log.error(f"❌ {check_name}异常: {e}")

            # 检查是否有失败的检查项
            if failed_checks:
                log.warning(f"⚠️ 以下前置检查失败: {', '.join(failed_checks)}")
                log.warning("⚠️ 测试可能无法正常执行，请检查环境配置")
                log.info("💡 提示: 可以使用 --skip-precheck 选项跳过前置检查")

                # 可以选择是否继续执行测试
                # 这里选择继续执行，但会记录警告
                # 如果需要强制退出，可以取消注释下面的行
                # pytest.exit(f"前置检查失败: {', '.join(failed_checks)}")
            else:
                log.info("✅ 所有前置检查通过")

        # 设置批量测试屏幕为常亮
        log.info("📱 设置批量测试屏幕管理...")
        BaseEllaTest.setup_batch_test_screen()

        log.info("🎯 Ella测试环境准备完成，开始执行测试用例")

    except Exception as e:
        log.error(f"❌ 前置检查或设置失败: {e}")
        # 如果前置检查失败，可以选择退出测试
        # pytest.exit(f"前置检查失败: {e}")


def pytest_sessionfinish(session, exitstatus):
    """
    pytest会话结束时的钩子函数
    在所有测试结束后执行一次
    """
    try:
        # 导入BaseEllaTest类
        from testcases.test_ella.base_ella_test import BaseEllaTest
        
        log.info("🔄 Ella测试会话结束，恢复屏幕设置...")
        
        # 恢复屏幕设置为10分钟超时
        BaseEllaTest.restore_batch_test_screen()
        
        log.info("✅ 批量测试屏幕设置恢复完成")
        
        # 记录测试会话结果
        if exitstatus == 0:
            log.info("🎉 所有测试执行成功")
        else:
            log.warning(f"⚠️ 测试执行完成，退出状态: {exitstatus}")
            
    except Exception as e:
        log.error(f"❌ 批量测试屏幕设置恢复失败: {e}")


def pytest_runtest_setup(item):
    """
    每个测试用例开始前的钩子函数
    在这里启动录屏功能
    """
    try:
        # 导入BaseEllaTest类
        from testcases.test_ella.base_ella_test import BaseEllaTest

        # 获取测试名称
        test_name = item.name
        test_class_name = item.cls.__name__ if item.cls else "unknown"

        log.info(f"🎬 测试用例开始前准备: {test_class_name}::{test_name}")

        # 检查是否是BaseEllaTest的子类
        if item.cls and issubclass(item.cls, BaseEllaTest):
            # 检查是否有no_recording标记
            if item.get_closest_marker("no_recording"):
                log.info(f"📝 跳过标记为no_recording的测试: {test_name}")
                return

            # 创建测试实例来访问录屏功能
            test_instance = item.cls()

            # 确定录屏质量
            recording_quality = "medium"  # 默认质量

            # 检查是否有high_quality_recording标记
            if item.get_closest_marker("high_quality_recording"):
                recording_quality = "high"
                log.info(f"🎬 使用高质量录屏: {test_name}")

            # 从pytest配置获取质量设置
            if hasattr(item.session.config, 'getoption'):
                config_quality = item.session.config.getoption("--recording-quality", None)
                if config_quality:
                    recording_quality = config_quality

            # 开始录屏
            success = test_instance.start_test_recording(test_name, quality=recording_quality)
            if success:
                log.info(f"🎬 测试用例录屏开始: {test_name} (质量: {recording_quality})")
            else:
                log.warning(f"⚠️ 测试用例录屏开始失败: {test_name}")
        else:
            log.debug(f"📝 跳过非Ella测试的录屏: {test_name}")

    except Exception as e:
        log.error(f"❌ 测试用例开始前录屏设置失败: {e}")
        # 不阻止测试继续执行


def pytest_runtest_teardown(item, nextitem):
    """
    每个测试用例结束后的钩子函数
    在这里停止录屏并附加到Allure报告
    """
    try:
        # 导入BaseEllaTest类
        from testcases.test_ella.base_ella_test import BaseEllaTest

        # 获取测试名称和结果
        test_name = item.name
        test_class_name = item.cls.__name__ if item.cls else "unknown"

        log.info(f"🎬 测试用例结束后清理: {test_class_name}::{test_name}")

        # 清理所有运行中的应用进程
        from pages.apps.ella.dialogue_page import EllaDialoguePage
        ella_app = EllaDialoguePage()
        ella_app.clear_recent_apps()

        # 检查是否是BaseEllaTest的子类
        if item.cls and issubclass(item.cls, BaseEllaTest):
            # 创建测试实例来访问录屏功能
            test_instance = item.cls()

            # 停止录屏并附加到Allure报告
            recording_file = test_instance.stop_test_recording(attach_to_allure=True)
            if recording_file:
                log.info(f"🎬 测试用例录屏完成: {test_name} -> {recording_file}")
            else:
                log.debug(f"📝 测试用例无录屏文件: {test_name}")
        else:
            log.debug(f"📝 跳过非Ella测试的录屏清理: {test_name}")

    except Exception as e:
        log.error(f"❌ 测试用例结束后录屏清理失败: {e}")
        # 不阻止测试继续执行


def pytest_addoption(parser):
    """
    添加自定义命令行选项
    """
    parser.addoption(
        "--skip-precheck",
        action="store_true",
        default=False,
        help="跳过前置检查（设备连接、WhatsApp安装等）"
    )
    parser.addoption(
        "--force-whatsapp-install",
        action="store_true",
        default=False,
        help="强制重新安装WhatsApp（即使已安装）"
    )
    parser.addoption(
        "--disable-recording",
        action="store_true",
        default=False,
        help="禁用测试录屏功能"
    )
    parser.addoption(
        "--recording-quality",
        choices=["low", "medium", "high"],
        default="medium",
        help="录屏质量设置 (low: 720p/4M, medium: 1080p/6M, high: 1080p/8M)"
    )


def pytest_configure(config):
    """
    pytest配置钩子函数
    可以在这里添加全局配置
    """
    # 注册自定义标记
    config.addinivalue_line(
        "markers", "screen_management: 标记需要特殊屏幕管理的测试"
    )
    config.addinivalue_line(
        "markers", "long_running: 标记长时间运行的测试"
    )
    config.addinivalue_line(
        "markers", "quick_test: 标记快速测试"
    )
    config.addinivalue_line(
        "markers", "no_recording: 标记不需要录屏的测试"
    )
    config.addinivalue_line(
        "markers", "high_quality_recording: 标记需要高质量录屏的测试"
    )

    # 可以在这里添加屏幕管理的全局配置
    log.info("📋 Ella测试配置初始化完成")

    # 记录配置选项
    if config.getoption("--skip-precheck"):
        log.info("⚠️ 已启用跳过前置检查选项")
    if config.getoption("--force-whatsapp-install"):
        log.info("🔄 已启用强制重新安装WhatsApp选项")
    if config.getoption("--disable-recording"):
        log.info("🎬 已禁用录屏功能")
        # 设置录屏配置
        try:
            from testcases.test_ella.base_ella_test import BaseEllaTest
            BaseEllaTest.set_recording_config(enabled=False)
        except Exception as e:
            log.error(f"设置录屏配置失败: {e}")
    else:
        recording_quality = config.getoption("--recording-quality")
        log.info(f"🎬 录屏质量设置: {recording_quality}")


def pytest_unconfigure(config):
    """
    pytest取消配置钩子函数
    在pytest退出前执行
    """
    log.info("📋 Ella测试配置清理完成")


# 自定义pytest标记已在pytest_configure函数中注册


# 自定义fixture，用于特殊的屏幕管理需求
@pytest.fixture
def screen_never_timeout():
    """
    为特定测试设置屏幕永不超时的fixture
    使用方法：在测试函数参数中添加 screen_never_timeout
    """
    try:
        from testcases.test_ella.base_ella_test import BaseEllaTest
        
        log.info("🔧 为当前测试设置屏幕永不超时...")
        screen_manager = BaseEllaTest.get_screen_manager()
        
        # 保存当前超时设置
        original_timeout = screen_manager.get_current_screen_timeout()
        
        # 设置永不超时
        screen_manager.set_screen_never_timeout()
        
        yield
        
        # 恢复原始超时设置
        if original_timeout:
            screen_manager.set_screen_timeout(original_timeout)
            log.info(f"🔄 恢复原始屏幕超时设置: {original_timeout}ms")
            
    except Exception as e:
        log.error(f"❌ 屏幕永不超时设置失败: {e}")
        yield


@pytest.fixture
def screen_quick_timeout():
    """
    为快速测试设置较短超时时间的fixture
    使用方法：在测试函数参数中添加 screen_quick_timeout
    """
    try:
        from testcases.test_ella.base_ella_test import BaseEllaTest
        
        log.info("🔧 为快速测试设置5分钟屏幕超时...")
        screen_manager = BaseEllaTest.get_screen_manager()
        
        # 保存当前超时设置
        original_timeout = screen_manager.get_current_screen_timeout()
        
        # 设置5分钟超时
        screen_manager.set_screen_timeout_minutes(5)
        
        yield
        
        # 恢复原始超时设置
        if original_timeout:
            screen_manager.set_screen_timeout(original_timeout)
            log.info(f"🔄 恢复原始屏幕超时设置: {original_timeout}ms")
            
    except Exception as e:
        log.error(f"❌ 快速测试屏幕超时设置失败: {e}")
        yield


@pytest.fixture
def disable_screen_management():
    """
    禁用屏幕管理的fixture
    使用方法：在测试函数参数中添加 disable_screen_management
    """
    try:
        from testcases.test_ella.base_ella_test import BaseEllaTest
        
        log.info("🔧 为当前测试禁用屏幕管理...")
        
        # 保存原始配置
        original_enabled = BaseEllaTest._screen_management_enabled
        
        # 禁用屏幕管理
        BaseEllaTest.set_screen_management_config(enabled=False)
        
        yield
        
        # 恢复原始配置
        BaseEllaTest.set_screen_management_config(enabled=original_enabled)
        log.info("🔄 恢复屏幕管理配置")
        
    except Exception as e:
        log.error(f"❌ 禁用屏幕管理失败: {e}")
        yield
