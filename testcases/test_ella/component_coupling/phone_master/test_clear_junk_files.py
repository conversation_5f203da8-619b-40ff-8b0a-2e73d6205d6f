"""
Ella语音助手打开应用指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("打开应用")
class TestEllaClearJunkFiles(SimpleEllaTest):
    @allure.title("测试clear junk files命令")
    @allure.description("使用简化的测试框架测试phonemaster开启命令，验证响应包含Done且实际打开PhoneMaster")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_clear_junk_files(self, ella_app):
        """测试clear junk files命令 - 简洁版本"""
        command = "clear junk files"

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command, verify_files=False, verify_status=True,
            )

        with allure.step("验证响应包含Done"):
            result = self.verify_expected_in_response("done", response_text)
            assert result, f"响应文本应包含'Done'，实际响应: '{response_text}'"

        with allure.step(f"验证PhoneMaster应用已打开"):
            assert final_status, f"PhoneMaster应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            # 添加额外的验证信息
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")

        # pytest测试函数不应该返回值，所有验证都应该通过assert完成
