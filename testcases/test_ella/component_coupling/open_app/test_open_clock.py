"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("open clock")
class TestEllaCommandConcise(SimpleEllaTest):
    """Ella语音助手基础指令"""

    @allure.title("open clock")
    @allure.description("使用open clock命令，验证响应包含Done且实际打开clock命令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_open_clock(self, ella_app):
        """测试open clock命令 - 简洁版本"""
        command = "open clock"

        app_name = 'clock'

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command
            )

        with allure.step("验证响应包含Done"):
            expected_text =["done"]
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含'Done'，实际响应: '{response_text}'"

        with allure.step(f"验证{app_name}已打开"):
            assert final_status, f"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            # 添加额外的验证信息
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")

        # pytest测试函数不应该返回值，所有验证都应该通过assert完成
