"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("打开应用")
class TestEllaOpenPlayPoliticalNews(SimpleEllaTest):
    """Ella打开play media by visha测试类"""

    @allure.title("测试play media by visha")
    @allure.description("测试play media by visha指令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_play_music_by_visha(self, ella_app):
        """测试play media by visha命令"""
        command = "play media by visha"
        # expected_text = ['I need to download visha for android to continue']
        # expected_text = ['not installed yet. Please download the app and try again.']
        expected_text = ['Done']

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command
            )

        with allure.step("验证响应包含期望内容"):
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"


        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
