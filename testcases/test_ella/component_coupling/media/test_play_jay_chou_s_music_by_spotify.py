"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("打开应用")
class TestEllaOpenMusic(SimpleEllaTest):
    """Ella打开music测试类"""

    @allure.title("测试play jay chou's media by spotify")
    @allure.description("测试play jay chou's media by spotify指令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_play_jay_chou_s_music_by_spotify(self, ella_app):
        """测试play jay chou's media by spotify命令"""
        command = "play jay chou's media by spotify"
        expected_text = ['Spotify is not installed yet',
                         'I need to download Spotify'
                         ]
        app_name = 'media'

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command, verify_status=False, verify_files=False
            )

        with allure.step("验证响应包含期望内容"):
            result = self.verify_expected_in_response_advanced(expected_text, response_text,match_any=True)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"

        # with allure.step(f"验证{app_name}已打开"):
        #     assert final_status, f"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
