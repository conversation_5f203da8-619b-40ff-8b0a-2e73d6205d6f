"""
元素操作基类
封装UIAutomator2的元素操作方法
"""
import time
from typing import Optional, Union, List, Tuple
from uiautomator2 import UiObject
from core.logger import log
from core.base_driver import driver_manager
from utils.yaml_utils import YamlUtils


class BaseElement:
    """元素操作基类"""
    
    def __init__(self, locator: dict, description: str = ""):
        """
        初始化元素
        
        Args:
            locator: 元素定位器字典，如 {"text": "确定"} 或 {"resourceId": "com.example:id/button"}
            description: 元素描述，用于日志输出
        """
        self.locator = locator
        self.description = description or str(locator)
        self.driver = driver_manager.driver
        
        # 加载超时配置
        try:
            config_path = YamlUtils.get_config_path("config.yaml")
            config = YamlUtils.load_yaml(config_path)
            # log.info(f"✅ 配置信息: {config}")
            self.timeout = config.get("app", {}).get("element_timeout", 5)
        except Exception:
            self.timeout = 5
    
    def _get_element(self) -> UiObject:
        """
        获取元素对象
        
        Returns:
            UiObject: UIAutomator2元素对象
        """
        try:
            return self.driver(**self.locator)
        except Exception as e:
            log.error(f"获取元素失败 [{self.description}]: {e}")
            raise
    
    def wait_for_element(self, timeout: Optional[float] = None) -> bool:
        """
        等待元素出现
        
        Args:
            timeout: 超时时间，为None时使用默认超时时间
            
        Returns:
            bool: 元素是否出现
        """
        timeout = timeout or self.timeout
        try:
            log.info(f"等待元素出现 [{self.description}], 超时时间: {timeout}秒")
            element = self._get_element()
            result = element.wait(timeout=timeout)
            log.info(f"元素列表 [{result}]")
            if result:
                log.info(f"元素已出现 [{self.description}]")
            else:
                log.warning(f"元素等待超时 [{self.description}]")
            return result
        except Exception as e:
            log.error(f"等待元素失败 [{self.description}]: {e}")
            return False
    
    def wait_for_element_gone(self, timeout: Optional[float] = None) -> bool:
        """
        等待元素消失
        
        Args:
            timeout: 超时时间，为None时使用默认超时时间
            
        Returns:
            bool: 元素是否消失
        """
        timeout = timeout or self.timeout
        try:
            log.info(f"等待元素消失 [{self.description}], 超时时间: {timeout}秒")
            element = self._get_element()
            result = element.wait_gone(timeout=timeout)
            if result:
                log.info(f"元素已消失 [{self.description}]")
            else:
                log.warning(f"元素消失等待超时 [{self.description}]")
            return result
        except Exception as e:
            log.error(f"等待元素消失失败 [{self.description}]: {e}")
            return False
    
    def is_exists(self) -> bool:
        """
        检查元素是否存在 - 改进版本，包含错误处理和重试机制

        Returns:
            bool: 元素是否存在
        """
        max_retries = 3
        retry_delay = 1
        version_error_handled = False

        for attempt in range(max_retries):
            try:
                element = self._get_element()
                exists = element.exists
                log.debug(f"元素存在性检查 [{self.description}]: {exists}")
                return exists

            except Exception as e:
                error_msg = str(e).lower()

                # 特别处理版本错误
                if 'invalid version' in error_msg and not version_error_handled:
                    log.warning(f"检测到版本错误，尝试自动修复 [{self.description}]: {e}")
                    if self._handle_version_error():
                        version_error_handled = True
                        # 重置重试计数，给修复后的连接一个机会
                        max_retries += 2
                        time.sleep(2)  # 等待服务稳定
                        continue
                    else:
                        log.error(f"版本错误修复失败 [{self.description}]")
                        return False

                # 检查是否是已知的可重试错误
                if self._is_retryable_error(error_msg):
                    if attempt < max_retries - 1:
                        log.warning(f"元素检查遇到可重试错误 [{self.description}] (尝试 {attempt + 1}/{max_retries}): {e}")
                        time.sleep(retry_delay)
                        continue
                    else:
                        log.error(f"元素检查重试失败 [{self.description}]: {e}")
                else:
                    log.error(f"检查元素存在性失败 [{self.description}]: {e}")

                return False

        return False
    def is_exists_v1(self) -> bool:
        """
        检查元素是否存在 - 改进版本，包含错误处理和重试机制

        Returns:
            bool: 元素是否存在
        """
        max_retries = 3
        retry_delay = 1

        for attempt in range(max_retries):
            element = self._get_element()
            exists = element.exists
            log.debug(f"元素存在性检查 [{self.description}]: {exists}")
            if exists:
                log.debug(f"元素存在 [{self.description}]")
                return True
            else:
                log.debug(f"元素不存在 [{self.description}]，重新检查")
                time.sleep(retry_delay)

        log.error(f"元素检查重试失败")
        return False

    def _is_retryable_error(self, error_msg: str) -> bool:
        """
        判断错误是否可重试

        Args:
            error_msg: 错误消息（小写）

        Returns:
            bool: 是否可重试
        """
        retryable_errors = [
            'invalid version',
            'connection refused',
            'timeout',
            'service not available',
            'uiautomator not running',
            'accessibility service',
            'null pointer',
            'remote service exception',
            'version: \'\'',  # 特别处理空版本字符串
            'version=""',     # 另一种空版本格式
            'connection reset',
            'broken pipe',
            'device offline'
        ]

        return any(error in error_msg for error in retryable_errors)

    def _handle_version_error(self) -> bool:
        """
        处理版本相关错误

        Returns:
            bool: 是否处理成功
        """
        try:
            log.warning(f"检测到版本错误，尝试自动修复 [{self.description}]")

            # 导入UIAutomator2管理器
            from utils.uiautomator2_manager import uiautomator2_manager
            from utils.device_config_manager import device_config_manager

            # 获取当前设备ID
            try:
                config = device_config_manager.load_current_config()
                current_device = config.get('current_device', 'default')
                device_config = config.get('devices', {}).get(current_device, {})
                device_id = device_config.get('device_id')
            except:
                device_id = None

            # 尝试修复版本问题
            if uiautomator2_manager.fix_version_issue(device_id):
                log.info(f"版本问题修复成功，重新获取驱动 [{self.description}]")

                # 重新获取驱动实例
                from core.base_driver import driver_manager
                driver_manager._driver = None  # 强制重新连接
                self.driver = driver_manager.driver

                return True
            else:
                log.error(f"版本问题修复失败 [{self.description}]")
                return False

        except Exception as e:
            log.error(f"处理版本错误失败 [{self.description}]: {e}")
            return False
    
    def click(self, timeout: Optional[float] = None) -> bool:
        """
        点击元素
        
        Args:
            timeout: 等待超时时间
            
        Returns:
            bool: 点击是否成功
        """
        try:
            if self.wait_for_element(timeout):
                element = self._get_element()
                element.click()
                log.info(f"点击元素成功 [{self.description}]")
                return True
            else:
                log.error(f"点击失败，元素不存在 [{self.description}]")
                return False
        except Exception as e:
            log.error(f"点击元素失败 [{self.description}]: {e}")
            return False
    
    def double_click(self, timeout: Optional[float] = None) -> bool:
        """
        双击元素
        
        Args:
            timeout: 等待超时时间
            
        Returns:
            bool: 双击是否成功
        """
        try:
            if self.wait_for_element(timeout):
                element = self._get_element()
                element.double_click()
                log.info(f"双击元素成功 [{self.description}]")
                return True
            else:
                log.error(f"双击失败，元素不存在 [{self.description}]")
                return False
        except Exception as e:
            log.error(f"双击元素失败 [{self.description}]: {e}")
            return False
    
    def long_click(self, duration: float = 1.0, timeout: Optional[float] = None) -> bool:
        """
        长按元素
        
        Args:
            duration: 长按持续时间(秒)
            timeout: 等待超时时间
            
        Returns:
            bool: 长按是否成功
        """
        try:
            if self.wait_for_element(timeout):
                element = self._get_element()
                element.long_click(duration=duration)
                log.info(f"长按元素成功 [{self.description}], 持续时间: {duration}秒")
                return True
            else:
                log.error(f"长按失败，元素不存在 [{self.description}]")
                return False
        except Exception as e:
            log.error(f"长按元素失败 [{self.description}]: {e}")
            return False

    def send_keys(self, text: str, timeout: Optional[float] = None) -> bool:
        """
        输入文本

        Args:
            text: 要输入的文本
            timeout: 等待超时时间

        Returns:
            bool: 输入是否成功
        """
        try:
            if self.wait_for_element(timeout):
                element = self._get_element()
                element.set_text(text)
                log.info(f"输入文本成功 [{self.description}]: {text}")
                return True
            else:
                log.error(f"输入文本失败，元素不存在 [{self.description}]")
                return False
        except Exception as e:
            log.error(f"输入文本失败 [{self.description}]: {e}")
            return False

    def clear_text(self, timeout: Optional[float] = None) -> bool:
        """
        清空文本

        Args:
            timeout: 等待超时时间

        Returns:
            bool: 清空是否成功
        """
        try:
            if self.wait_for_element(timeout):
                element = self._get_element()
                element.clear_text()
                log.info(f"清空文本成功 [{self.description}]")
                return True
            else:
                log.error(f"清空文本失败，元素不存在 [{self.description}]")
                return False
        except Exception as e:
            log.error(f"清空文本失败 [{self.description}]: {e}")
            return False

    def get_text(self, timeout: Optional[float] = None) -> Optional[str]:
        """
        获取元素文本

        Args:
            timeout: 等待超时时间

        Returns:
            Optional[str]: 元素文本，获取失败返回None
        """
        try:
            if self.wait_for_element(timeout):
                element = self._get_element()
                text = element.get_text()
                log.debug(f"获取元素文本 [{self.description}]: {text}")
                return text
            else:
                log.error(f"获取文本失败，元素不存在 [{self.description}]")
                return None
        except Exception as e:
            log.error(f"获取元素文本失败 [{self.description}]: {e}")
            return None

    def get_attribute(self, attribute: str, timeout: Optional[float] = None) -> Optional[str]:
        """
        获取元素属性

        Args:
            attribute: 属性名
            timeout: 等待超时时间

        Returns:
            Optional[str]: 属性值，获取失败返回None
        """
        try:
            if self.wait_for_element(timeout):
                element = self._get_element()
                info = element.info
                value = info.get(attribute)
                log.debug(f"获取元素属性 [{self.description}] {attribute}: {value}")
                return value
            else:
                log.error(f"获取属性失败，元素不存在 [{self.description}]")
                return None
        except Exception as e:
            log.error(f"获取元素属性失败 [{self.description}]: {e}")
            return None

    def is_enabled(self, timeout: Optional[float] = None) -> bool:
        """
        检查元素是否可用

        Args:
            timeout: 等待超时时间

        Returns:
            bool: 元素是否可用
        """
        try:
            if self.wait_for_element(timeout):
                element = self._get_element()
                enabled = element.info.get("enabled", False)
                log.debug(f"元素可用性检查 [{self.description}]: {enabled}")
                return enabled
            else:
                return False
        except Exception as e:
            log.error(f"检查元素可用性失败 [{self.description}]: {e}")
            return False

    def is_selected(self, timeout: Optional[float] = None) -> bool:
        """
        检查元素是否被选中

        Args:
            timeout: 等待超时时间

        Returns:
            bool: 元素是否被选中
        """
        try:
            if self.wait_for_element(timeout):
                element = self._get_element()
                selected = element.info.get("selected", False)
                log.debug(f"元素选中状态检查 [{self.description}]: {selected}")
                return selected
            else:
                return False
        except Exception as e:
            log.error(f"检查元素选中状态失败 [{self.description}]: {e}")
            return False

    def get_bounds(self, timeout: Optional[float] = None) -> Optional[Tuple[int, int, int, int]]:
        """
        获取元素边界坐标

        Args:
            timeout: 等待超时时间

        Returns:
            Optional[Tuple]: (left, top, right, bottom) 坐标，获取失败返回None
        """
        try:
            if self.wait_for_element(timeout):
                element = self._get_element()
                bounds = element.info.get("bounds")
                if bounds:
                    # bounds格式: {"left": x1, "top": y1, "right": x2, "bottom": y2}
                    result = (bounds["left"], bounds["top"], bounds["right"], bounds["bottom"])
                    log.debug(f"获取元素边界 [{self.description}]: {result}")
                    return result
                return None
            else:
                return None
        except Exception as e:
            log.error(f"获取元素边界失败 [{self.description}]: {e}")
            return None
