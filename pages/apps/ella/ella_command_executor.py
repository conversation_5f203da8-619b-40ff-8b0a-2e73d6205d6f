"""
Ella命令执行器
负责执行各种类型的命令（文本、语音等）
"""
import time
from core.logger import log

# 导入TTS工具
from utils.tts_utils import TTSManager

class EllaCommandExecutor:
    """Ella命令执行器"""
    
    def __init__(self, driver=None, page_elements=None):
        """
        初始化命令执行器
        
        Args:
            driver: UIAutomator2驱动实例
            page_elements: 页面元素字典
        """
        self.driver = driver
        self.page_elements = page_elements or {}


        # 创建TTS管理器实例
        self.tts_manager = TTSManager()
    
    def execute_text_command(self, command: str) -> bool:
        """
        执行文本命令（输入+发送）
        
        Args:
            command: 要执行的命令
            
        Returns:
            bool: 执行是否成功
        """
        try:
            log.info(f"执行文本命令: {command}")
            
            if not self.driver:
                log.error("驱动实例未初始化")
                return False
            
            # 1. 确保输入框可用
            if not self._ensure_input_box_ready():
                log.error("无法确保输入框就绪")
                return False
            
            # 2. 输入命令
            if not self._input_text_command(command):
                return False
            
            # 3. 发送命令
            if not self._send_command():
                return False
            
            log.info("✅ 文本命令执行完成")
            return True
            
        except Exception as e:
            log.error(f"执行文本命令失败: {e}")
            return False
    
    def execute_voice_command(self, command: str, duration: float = 3.0, language: str = 'zh-CN') -> bool:
        """
        执行语音命令
        
        Args:
            command: 要执行的命令
            duration: 语音持续时间
            language: 语言代码
            
        Returns:
            bool: 执行是否成功
        """
        try:
            log.info(f"🎤 执行语音命令: '{command}' (语言: {language}, 持续时间: {duration}秒)")
            
            if not self.driver:
                log.error("驱动实例未初始化")
                return False
            
            # 1. 确保进入语音输入模式
            if not self._ensure_voice_input_box_ready():
                log.error("无法确保输入框就绪")
                return False

            # # 播放语音命令文件-先调用media播放，避免后续延迟
            # log.info(f"🔊 播放语音命令文件-先调用media播放，避免后续延迟: '{command}'")
            # self.play_voice_command_file(command, language)
            
            # 2. 启动语音输入
            if not self._start_voice_input():
                log.warning("无法启动语音输入")
                # return self.execute_text_command(command)
            
            # # 3. 等待语音录制状态稳定
            # log.info("等待语音录制状态稳定...")
            # time.sleep(1.0)
            
            # 4. 播放语音命令文件
            log.info(f"🔊 播放语音命令: '{command}'")
            voice_success = self.play_voice_command_file(command, language)
            
            if not voice_success:
                log.error("语音文件播放失败")
                # # 先停止语音输入
                # self._stop_voice_input()
                # return self.execute_text_command(command)
            
            # 5. 等待语音识别完成
            time.sleep(duration)
            
            # # 6. 停止语音输入
            # if not self._stop_voice_input():
            #     log.warning("停止语音输入失败，但继续执行")
            
            log.info("✅ 语音命令执行完成")
            return True
            
        except Exception as e:
            log.error(f"执行语音命令失败: {e}")
            # 尝试停止语音输入
            try:
                self._stop_voice_input()
            except:
                pass
            return False
    
    def _ensure_input_box_ready(self) -> bool:
        """
        确保输入框就绪
        
        Returns:
            bool: 输入框是否就绪
        """
        try:
            log.info("确保输入框就绪...")
            
            if not self.driver:
                return False
            
            # 检查已知的输入元素
            if self._check_known_input_elements():
                return True
            
            # 检查通用输入元素
            if self._check_generic_input_elements():
                return True
            
            # # 尝试通过坐标激活输入
            # if self._activate_input_by_coordinates():
            #     return True
            
            log.error("❌ 无法确保输入框就绪")
            return False
            
        except Exception as e:
            log.error(f"确保输入框就绪失败: {e}")
            return False

    def _ensure_voice_input_box_ready(self) -> bool:
        """确保语音输入就绪"""
        try:
            log.info("确保语音输入就绪...")

            if not self.driver:
                return False

            # 检查已知的语音输入按钮
            if self._check_known_voice_buttons():
                return True

            log.error("❌ 无法确保语音输入就绪")
            return False

        except Exception as e:
            log.error(f"确保语音输入就绪失败: {e}")
            return False
    def _check_known_voice_buttons(self) -> bool:
        """检查已知的语音输入按钮"""
        try:
            # 检查主语音按钮
            voice_button = self.page_elements.get('voice_input_button')
            if voice_button and voice_button.is_exists():
                log.info("✅ 找到语音输入按钮")
                return True
            return False
        except Exception as e:
            log.debug(f"检查语音输入按钮失败: {e}")
            return False
    def _check_known_input_elements(self) -> bool:
        """检查已知的输入元素"""
        try:
            # 检查主输入框
            input_box = self.page_elements.get('input_box')
            if input_box and input_box.is_exists():
                log.info("✅ 找到主输入框")
                return True
            
            # 检查备选输入框
            text_input_box = self.page_elements.get('text_input_box')
            if text_input_box and text_input_box.is_exists():
                log.info("✅ 找到备选输入框")
                return True
            
            return False
        except Exception as e:
            log.debug(f"检查已知输入元素失败: {e}")
            return False
    
    def _check_generic_input_elements(self) -> bool:
        """检查通用输入元素"""
        try:
            # 查找EditText元素
            edit_texts = self.driver(className="android.widget.EditText")
            if edit_texts.exists():
                log.info("✅ 找到EditText元素")
                return True
            
            return False
        except Exception as e:
            log.debug(f"检查通用输入元素失败: {e}")
            return False
    
    def _activate_input_by_coordinates(self) -> bool:
        """通过坐标激活输入"""
        try:
            # 获取屏幕尺寸
            width, height = self.driver.window_size()
            
            # 尝试点击屏幕下方中央（通常是输入框位置）
            input_x = width // 2
            input_y = int(height * 0.85)  # 屏幕下方85%位置
            
            log.info(f"尝试点击坐标激活输入: ({input_x}, {input_y})")
            self.driver.click(input_x, input_y)
            time.sleep(1)
            
            # 检查是否激活了输入
            return self._verify_input_activated()
            
        except Exception as e:
            log.debug(f"坐标激活输入失败: {e}")
            return False
    
    def _verify_input_activated(self) -> bool:
        """验证输入是否已激活"""
        try:
            # 检查是否有键盘出现或输入框获得焦点
            # 这里可以添加更多的验证逻辑
            return True  # 简化实现
        except Exception as e:
            log.debug(f"验证输入激活失败: {e}")
            return False
    
    def _input_text_command(self, command: str) -> bool:
        """
        输入文本命令
        
        Args:
            command: 要输入的命令文本
            
        Returns:
            bool: 输入是否成功
        """
        try:
            log.info(f"输入文本命令: {command}")
            
            # 清空输入框
            self._clear_input_box()
            
            # 尝试多种输入方法
            input_methods = [
                self._input_via_known_elements,
                self._input_via_generic_elements,
                self._input_via_coordinates
            ]
            
            for method in input_methods:
                try:
                    if method(command):
                        # 验证输入是否成功
                        if self._verify_input_text(command):
                            log.info("✅ 文本输入成功")
                            return True
                except Exception as e:
                    log.debug(f"输入方法失败: {e}")
                    continue
            
            log.error("❌ 所有输入方法都失败")
            return False
            
        except Exception as e:
            log.error(f"输入文本命令失败: {e}")
            return False
    
    def _input_via_known_elements(self, command: str) -> bool:
        """通过已知元素输入"""
        input_box = self.page_elements.get('input_box')
        if input_box and input_box.is_exists():
            input_box.send_keys(command)
            return True
        return False
    
    def _input_via_generic_elements(self, command: str) -> bool:
        """通过通用元素输入"""
        edit_texts = self.driver(className="android.widget.EditText")
        if edit_texts.exists():
            edit_texts.send_keys(command)
            return True
        return False
    
    def _input_via_coordinates(self, command: str) -> bool:
        """通过坐标输入"""
        try:
            # 先点击激活输入框
            width, height = self.driver.window_size()
            input_x = width // 2
            input_y = int(height * 0.85)
            
            self.driver.click(input_x, input_y)
            time.sleep(0.5)
            
            # 使用系统输入法输入
            self.driver.send_keys(command)
            return True
        except Exception:
            return False
    
    def _clear_input_box(self):
        """清空输入框"""
        try:
            # 尝试清空已知的输入框
            input_box = self.page_elements.get('input_box')
            if input_box and input_box.is_exists():
                input_box.clear_text()
                return
            
            # 尝试清空通用输入框
            edit_texts = self.driver(className="android.widget.EditText")
            if edit_texts.exists():
                edit_texts.clear_text()
                
        except Exception as e:
            log.debug(f"清空输入框失败: {e}")
    
    def _verify_input_text(self, expected_text: str) -> bool:
        """验证输入的文本"""
        try:
            # 简化实现，实际可以检查输入框内容
            return True
        except Exception:
            return False
    
    def _send_command(self) -> bool:
        """发送命令"""
        try:
            log.info("发送命令")
            
            # 尝试点击发送按钮
            send_button = self.page_elements.get('send_button')
            if send_button and send_button.is_exists():
                send_button.click()
                log.info("✅ 点击发送按钮成功")
                return True
            
            # 尝试按回车键
            self.driver.press("enter")
            log.info("✅ 按回车键发送成功")
            return True
            
        except Exception as e:
            log.error(f"发送命令失败: {e}")
            return False
    
    def _start_voice_input(self) -> bool:
        """启动语音输入 - 优化版本"""
        try:
            log.info("🎤 启动语音输入...")

            # 多重策略尝试点击语音按钮
            if self._try_click_voice_button_strategies():
                # 验证语音输入是否成功启动
                if self._verify_voice_input_started():
                    log.info("✅ 语音输入启动成功")
                    return True
                else:
                    log.warning("⚠️ 语音按钮点击成功，但语音输入未启动")
                    return False

            log.error("❌ 所有语音按钮点击策略都失败")
            return False

        except Exception as e:
            log.error(f"启动语音输入失败: {e}")
            return False

    def _try_click_voice_button_strategies(self) -> bool:
        """尝试多种策略点击语音按钮"""
        try:
            log.info("尝试多种策略点击语音按钮...")

            # 策略1: 主要语音按钮
            if self._try_primary_voice_button():
                return True

            # # 策略2: 备选语音按钮
            # if self._try_alternative_voice_button():
            #     return True
            #
            # # 策略3: 通过描述定位语音按钮
            # if self._try_voice_button_by_description():
            #     return True
            #
            # # 策略4: 通过坐标点击语音按钮
            # if self._try_voice_button_by_coordinates():
            #     return True
            #
            # # 策略5: 通过类名查找语音按钮
            # if self._try_voice_button_by_classname():
            #     return True

            return False

        except Exception as e:
            log.error(f"语音按钮点击策略失败: {e}")
            return False

    def _try_primary_voice_button(self) -> bool:
        """尝试点击主要语音按钮"""
        try:
            voice_button = self.page_elements.get('voice_input_button')
            if voice_button and voice_button.is_exists():
                log.info("✅ 找到主要语音按钮，尝试点击")
                if voice_button.click():
                    return True
                else:
                    log.warning("主要语音按钮点击失败")
            else:
                log.debug("主要语音按钮不存在，说明当前是文本输入模式")
            return False
        except Exception as e:
            log.debug(f"主要语音按钮点击异常: {e}")
            return False

    def _try_alternative_voice_button(self) -> bool:
        """尝试点击备选语音按钮"""
        try:
            voice_button_alt = self.page_elements.get('voice_button_alt')
            if voice_button_alt and voice_button_alt.is_exists():
                log.info("✅ 找到备选语音按钮，尝试点击")
                if voice_button_alt.click():
                    time.sleep(1.0)
                    return True
                else:
                    log.warning("备选语音按钮点击失败")
            else:
                log.debug("备选语音按钮不存在")
            return False
        except Exception as e:
            log.debug(f"备选语音按钮点击异常: {e}")
            return False

    def _try_voice_button_by_description(self) -> bool:
        """通过描述定位并点击语音按钮"""
        try:
            if not self.driver:
                return False

            # 尝试多种描述文本
            descriptions = ["语音输入", "voice", "麦克风", "Voice input", "Microphone"]

            for desc in descriptions:
                try:
                    voice_element = self.driver(description=desc)
                    if voice_element.exists():
                        log.info(f"✅ 通过描述'{desc}'找到语音按钮，尝试点击")
                        if voice_element.click():
                            time.sleep(1.0)
                            return True
                except Exception as e:
                    log.debug(f"描述'{desc}'定位失败: {e}")
                    continue

            log.debug("通过描述未找到语音按钮")
            return False
        except Exception as e:
            log.debug(f"描述定位语音按钮异常: {e}")
            return False

    def _try_voice_button_by_coordinates(self) -> bool:
        """通过坐标点击语音按钮"""
        try:
            if not self.driver:
                return False

            # 获取屏幕尺寸
            width, height = self.driver.window_size()

            # 常见的语音按钮位置（相对坐标）
            voice_positions = [
                (int(width * 0.9), int(height * 0.9)),   # 右下角
                (int(width * 0.85), int(height * 0.9)),  # 输入框右侧
                (int(width * 0.5), int(height * 0.95)),  # 底部中央
                (int(width * 0.95), int(height * 0.85)), # 右侧中下
            ]

            for x, y in voice_positions:
                try:
                    log.info(f"尝试点击坐标 ({x}, {y}) 寻找语音按钮")
                    self.driver.click(x, y)
                    time.sleep(1.0)

                    # 简单验证是否点击到了语音按钮
                    if self._quick_check_voice_mode():
                        log.info(f"✅ 坐标 ({x}, {y}) 点击成功，进入语音模式")
                        return True
                except Exception as e:
                    log.debug(f"坐标 ({x}, {y}) 点击失败: {e}")
                    continue

            log.debug("坐标点击未找到语音按钮")
            return False
        except Exception as e:
            log.debug(f"坐标定位语音按钮异常: {e}")
            return False

    def _try_voice_button_by_classname(self) -> bool:
        """通过类名查找并点击语音按钮"""
        try:
            if not self.driver:
                return False

            # 尝试查找ImageButton或ImageView类型的元素
            class_names = ["android.widget.ImageButton", "android.widget.ImageView"]

            for class_name in class_names:
                try:
                    elements = self.driver(className=class_name)
                    if elements.exists():
                        # 遍历所有该类型的元素，尝试点击
                        for i in range(min(elements.count, 5)):  # 最多尝试5个元素
                            try:
                                element = elements[i]
                                log.info(f"尝试点击第{i+1}个{class_name}元素")
                                if element.click():
                                    time.sleep(1.0)
                                    if self._quick_check_voice_mode():
                                        log.info(f"✅ 第{i+1}个{class_name}元素点击成功，进入语音模式")
                                        return True
                            except Exception as e:
                                log.debug(f"第{i+1}个{class_name}元素点击失败: {e}")
                                continue
                except Exception as e:
                    log.debug(f"类名'{class_name}'查找失败: {e}")
                    continue

            log.debug("通过类名未找到语音按钮")
            return False
        except Exception as e:
            log.debug(f"类名定位语音按钮异常: {e}")
            return False

    def _quick_check_voice_mode(self) -> bool:
        """快速检查是否进入语音模式"""
        try:
            # 这里可以添加更多的语音模式检测逻辑
            # 例如检查是否出现录音动画、麦克风图标变化等
            # 简化实现，返回True表示假设成功
            return True
        except Exception:
            return False

    def _verify_voice_input_started(self) -> bool:
        """验证语音输入是否成功启动"""
        try:
            log.info("验证语音输入启动状态...")

            # 等待语音输入界面稳定
            # time.sleep(0.5)

            # # 检查1: 查找录音相关的UI元素
            # if self._check_recording_indicators():
            #     log.info("✅ 检测到录音指示器")
            #     return True
            #
            # 检查2: 检查语音按钮状态变化
            if self._check_voice_button_state_change():
                log.info("✅ 检测到语音按钮状态变化")
                return True

            # 检查3: 简化验证 - 假设点击成功就是启动成功
            log.info("✅ 假设语音输入已启动（简化验证）")
            return True

        except Exception as e:
            log.error(f"验证语音输入启动失败: {e}")
            return False

    def _check_recording_indicators(self) -> bool:
        """检查录音指示器"""
        try:
            if not self.driver:
                return False

            # 查找可能的录音指示器
            recording_indicators = [
                {"text": "正在录音"},
                {"text": "Recording"},
                {"text": "Listening"},
                {"description": "录音"},
                {"description": "recording"},
                {"className": "android.widget.ProgressBar"},  # 录音进度条
            ]

            for indicator in recording_indicators:
                try:
                    element = self.driver(**indicator)
                    if element.exists():
                        log.debug(f"找到录音指示器: {indicator}")
                        return True
                except Exception:
                    continue

            return False
        except Exception:
            return False

    def _check_voice_button_state_change(self) -> bool:
        """检查语音按钮状态变化"""
        try:
            text_input_button = self.page_elements.get('text_input_button')
            if text_input_button and text_input_button.is_exists():
                log.debug("找到切换至文本输入模式按钮，表示语音按钮状态变化")
                return True
            return True
        except Exception:
            return False
    
    def _stop_voice_input(self) -> bool:
        """停止语音输入 - 优化版本"""
        try:
            log.info("🛑 停止语音输入...")

            # 多重策略尝试停止语音输入
            if self._try_stop_voice_input_strategies():
                # 验证语音输入是否成功停止
                if self._verify_voice_input_stopped():
                    log.info("✅ 语音输入停止成功")
                    return True
                else:
                    log.warning("⚠️ 语音停止操作执行，但状态验证失败")
                    return True  # 仍然返回True，因为操作已执行

            log.error("❌ 所有语音停止策略都失败")
            return False

        except Exception as e:
            log.error(f"停止语音输入失败: {e}")
            return False

    def _try_stop_voice_input_strategies(self) -> bool:
        """尝试多种策略停止语音输入"""
        try:
            log.info("尝试多种策略停止语音输入...")

            # 策略1: 再次点击主要语音按钮
            if self._try_stop_primary_voice_button():
                return True

            # 策略2: 再次点击备选语音按钮
            if self._try_stop_alternative_voice_button():
                return True

            # 策略3: 查找停止按钮
            if self._try_find_stop_button():
                return True

            # 策略4: 通过坐标点击停止
            if self._try_stop_by_coordinates():
                return True

            # 策略5: 按返回键停止
            if self._try_stop_by_back_key():
                return True

            return False

        except Exception as e:
            log.error(f"语音停止策略失败: {e}")
            return False

    def _try_stop_primary_voice_button(self) -> bool:
        """尝试点击主要语音按钮停止"""
        try:
            voice_button = self.page_elements.get('voice_input_button')
            if voice_button and voice_button.is_exists():
                log.info("✅ 再次点击主要语音按钮停止录音")
                if voice_button.click():
                    time.sleep(1.0)
                    return True
                else:
                    log.warning("主要语音按钮停止点击失败")
            else:
                log.debug("主要语音按钮不存在")
            return False
        except Exception as e:
            log.debug(f"主要语音按钮停止点击异常: {e}")
            return False

    def _try_stop_alternative_voice_button(self) -> bool:
        """尝试点击备选语音按钮停止"""
        try:
            voice_button_alt = self.page_elements.get('voice_button_alt')
            if voice_button_alt and voice_button_alt.is_exists():
                log.info("✅ 再次点击备选语音按钮停止录音")
                if voice_button_alt.click():
                    time.sleep(1.0)
                    return True
                else:
                    log.warning("备选语音按钮停止点击失败")
            else:
                log.debug("备选语音按钮不存在")
            return False
        except Exception as e:
            log.debug(f"备选语音按钮停止点击异常: {e}")
            return False

    def _try_find_stop_button(self) -> bool:
        """查找并点击停止按钮"""
        try:
            if not self.driver:
                return False

            # 查找可能的停止按钮
            stop_indicators = [
                {"text": "停止"},
                {"text": "Stop"},
                {"text": "完成"},
                {"text": "Done"},
                {"description": "停止"},
                {"description": "stop"},
                {"description": "完成"},
                {"description": "done"},
            ]

            for indicator in stop_indicators:
                try:
                    element = self.driver(**indicator)
                    if element.exists():
                        log.info(f"✅ 找到停止按钮: {indicator}")
                        if element.click():
                            time.sleep(1.0)
                            return True
                except Exception as e:
                    log.debug(f"停止按钮'{indicator}'点击失败: {e}")
                    continue

            log.debug("未找到停止按钮")
            return False
        except Exception as e:
            log.debug(f"查找停止按钮异常: {e}")
            return False

    def _try_stop_by_coordinates(self) -> bool:
        """通过坐标点击停止语音输入"""
        try:
            if not self.driver:
                return False

            # 获取屏幕尺寸
            width, height = self.driver.window_size()

            # 尝试点击语音按钮可能的位置来停止
            stop_positions = [
                (int(width * 0.9), int(height * 0.9)),   # 右下角
                (int(width * 0.85), int(height * 0.9)),  # 输入框右侧
                (int(width * 0.5), int(height * 0.5)),   # 屏幕中央
            ]

            for x, y in stop_positions:
                try:
                    log.info(f"尝试点击坐标 ({x}, {y}) 停止语音输入")
                    self.driver.click(x, y)
                    time.sleep(1.0)
                    return True  # 假设点击成功
                except Exception as e:
                    log.debug(f"坐标 ({x}, {y}) 停止点击失败: {e}")
                    continue

            return False
        except Exception as e:
            log.debug(f"坐标停止语音输入异常: {e}")
            return False

    def _try_stop_by_back_key(self) -> bool:
        """通过返回键停止语音输入"""
        try:
            if not self.driver:
                return False

            log.info("尝试按返回键停止语音输入")
            self.driver.press("back")
            time.sleep(1.0)
            return True
        except Exception as e:
            log.debug(f"返回键停止语音输入失败: {e}")
            return False

    def _verify_voice_input_stopped(self) -> bool:
        """验证语音输入是否成功停止"""
        try:
            log.info("验证语音输入停止状态...")

            # 等待UI状态稳定
            time.sleep(0.5)

            # 检查1: 录音指示器是否消失
            if not self._check_recording_indicators():
                log.info("✅ 录音指示器已消失")
                return True

            # 检查2: 检查是否回到正常输入状态
            if self._check_normal_input_state():
                log.info("✅ 已回到正常输入状态")
                return True

            # 简化验证 - 假设停止操作成功
            log.info("✅ 假设语音输入已停止（简化验证）")
            return True

        except Exception as e:
            log.error(f"验证语音输入停止失败: {e}")
            return False

    def _check_normal_input_state(self) -> bool:
        """检查是否回到正常输入状态"""
        try:
            # 检查输入框是否可用
            input_box = self.page_elements.get('input_box')
            if input_box and input_box.is_exists():
                return True

            # 检查备选输入框
            text_input_box = self.page_elements.get('text_input_box')
            if text_input_box and text_input_box.is_exists():
                return True

            return False
        except Exception:
            return False
    
    def play_voice_command_file(self, command: str, language: str = 'zh-CN', volume: float = 1.0) -> bool:
        """
        根据输入的command和language查找并播放对应的语音文件
        如果文件不存在，则自动生成后播放

        流程：
        1、接收command和language
        2、获取文件名称（使用tts_utils.py中的generate_filename方法）
        3、根据language和名称，在app_test\data\tts\下对应语言的语音文件
        4、如果不存在，则调用tts_utils.py中的generate_audio_file方法生成语言文件
        5、直接使用window系统自带播放器播放语音

        Args:
            command: 命令文本
            language: 语言代码 (zh-CN, en-US等)
            volume: 播放音量 (0.0-1.0)

        Returns:
            bool: 播放是否成功
        """
        try:
            log.info(f"🎵 播放命令语音: '{command}' (语言: {language})")

            # # 导入TTS工具
            # from utils.tts_utils import TTSManager

            # 创建TTS管理器实例
            # tts_manager = TTSManager
            tts_manager = self.tts_manager

            # 1. 获取文件名称（使用现有的generate_filename方法）
            filename = tts_manager.generate_filename(command, language)
            log.debug(f"生成的文件名: {filename}")

            # 2. 根据language获取对应语言目录下的文件路径
            lang_dir = tts_manager.get_language_dir(language)
            audio_file_path = lang_dir / filename

            log.debug(f"查找音频文件: {audio_file_path}")

            # 3. 检查文件是否存在
            if audio_file_path.exists() and tts_manager.verify_audio_file(str(audio_file_path)):
                log.info(f"✅ 找到现有音频文件: {audio_file_path.relative_to(tts_manager.project_root)}")
            else:
                # 4. 如果文件不存在，调用generate_audio_file方法生成
                log.info(f"🎤 音频文件不存在，开始生成: {filename}")
                success = tts_manager.generate_audio_file(command, str(audio_file_path), language)

                if not success:
                    log.error(f"❌ 生成音频文件失败: {command}")
                    return False

                log.info(f"✅ 音频文件生成成功: {audio_file_path.relative_to(tts_manager.project_root)}")

            # 5. 使用Windows系统自带播放器播放语音
            return self._play_with_windows_player(str(audio_file_path), volume)

        except Exception as e:
            log.error(f"❌ 播放命令语音失败: {e}")
            return False

    def _play_with_windows_player(self, audio_file: str, volume: float = 1.0) -> bool:
        """
        使用Windows系统自带播放器播放音频文件

        Args:
            audio_file: 音频文件路径
            volume: 音量 (0.0-1.0)

        Returns:
            bool: 播放是否成功
        """
        try:
            import subprocess
            import platform
            import os
            from pathlib import Path

            # 检查是否为Windows系统
            if platform.system().lower() != 'windows':
                log.warning("当前系统不是Windows，尝试使用默认播放器")
                return self._play_with_fallback_player(audio_file, volume)

            # 检查文件是否存在
            if not os.path.exists(audio_file):
                log.error(f"音频文件不存在: {audio_file}")
                return False

            # 验证音频文件
            # from utils.tts_utils import TTSManager
            # tts_manager = TTSManager()
            tts_manager = self.tts_manager
            if not tts_manager.verify_audio_file(audio_file):
                log.error(f"音频文件验证失败: {audio_file}")
                return False

            file_size = os.path.getsize(audio_file) / 1024  # KB
            log.info(f"🔊 使用Windows播放器播放: {os.path.basename(audio_file)} ({file_size:.1f}KB)")

            # 使用Windows默认播放器播放
            # 方法1: 使用start命令（推荐）
            try:
                # 使用start命令打开文件，Windows会使用默认的音频播放器
                result = subprocess.run(
                    ['start', '/wait', '', audio_file],
                    shell=True,
                    capture_output=True,
                    text=True,
                    timeout=30  # 30秒超时
                )

                if result.returncode == 0:
                    log.info("✅ Windows播放器播放完成")
                    return True
                else:
                    log.warning(f"start命令返回非零状态码: {result.returncode}")

            except subprocess.TimeoutExpired:
                log.warning("Windows播放器播放超时")
                return True  # 超时也认为播放成功，因为可能是播放时间较长
            except Exception as e:
                log.warning(f"start命令播放失败: {e}")

            # 方法2: 使用PowerShell播放（备用方案）
            try:
                log.debug("尝试使用PowerShell播放音频")
                powershell_cmd = f'''
                Add-Type -AssemblyName presentationCore
                $mediaPlayer = New-Object system.windows.media.mediaplayer
                $mediaPlayer.open([uri]"{audio_file}")
                $mediaPlayer.Volume = {volume}
                $mediaPlayer.Play()
                Start-Sleep -Seconds 1
                while($mediaPlayer.NaturalDuration.HasTimeSpan -eq $false) {{
                    Start-Sleep -Milliseconds 100
                }}
                $duration = $mediaPlayer.NaturalDuration.TimeSpan.TotalSeconds
                Start-Sleep -Seconds $duration
                $mediaPlayer.Stop()
                $mediaPlayer.Close()
                '''

                result = subprocess.run(
                    ['powershell', '-Command', powershell_cmd],
                    capture_output=True,
                    text=True,
                    timeout=60
                )

                if result.returncode == 0:
                    log.info("✅ PowerShell播放器播放完成")
                    return True
                else:
                    log.warning(f"PowerShell播放失败: {result.stderr}")

            except subprocess.TimeoutExpired:
                log.warning("PowerShell播放超时")
                return True
            except Exception as e:
                log.warning(f"PowerShell播放异常: {e}")

            # 方法3: 回退到原有的播放方法
            log.info("回退到原有播放方法")
            return self._play_with_fallback_player(audio_file, volume)

        except Exception as e:
            log.error(f"Windows播放器播放失败: {e}")
            return False

    def _play_with_fallback_player(self, audio_file: str, volume: float = 1.0) -> bool:
        """
        回退播放方法（使用TTS管理器的播放功能）

        Args:
            audio_file: 音频文件路径
            volume: 音量

        Returns:
            bool: 播放是否成功
        """
        try:
            from utils.tts_utils import TTSManager
            tts_manager = TTSManager()
            return tts_manager.play_audio(audio_file, volume)
        except Exception as e:
            log.error(f"回退播放方法失败: {e}")
            return False


if __name__ == '__main__':
    from pages.apps.ella.dialogue_page import EllaDialoguePage

    ella_page = EllaDialoguePage()
    # ella_page.start_app()
    # ella_page.wait_for_page_load()
    executor = EllaCommandExecutor(ella_page.driver, ella_page.page_elements)
    executor.execute_voice_command(command='calll mom by whatsapp', duration=3.0, language='en-US')
