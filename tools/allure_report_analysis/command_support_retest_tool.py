#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
失败用例批量重测工具
读取失败分析Excel文件，找出所有失败记录，组织成pytest套件进行批量复测

功能特性:
1. 自动读取最新的失败分析Excel文件
2. 支持过滤特定失败原因的用例
3. 支持重测所有失败用例
4. 自动生成pytest重测脚本
5. 支持生成Allure测试报告

作者: AI Assistant
创建时间: 2025-09-04
"""

import os
import sys
import pandas as pd
import subprocess
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Set, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from core.logger import log


class FailureRetestTool:
    """失败用例批量重测工具"""

    def __init__(self, excel_path: str = None, filter_reasons: List[str] = None):
        """
        初始化重测工具

        Args:
            excel_path: Excel文件路径，如果为None则自动查找最新的
            filter_reasons: 要过滤的失败原因列表，如果为None则包含所有失败用例
        """
        self.project_root = Path(__file__).parent.parent.parent
        self.excel_path = excel_path or self._find_latest_failure_analysis_excel()
        self.filtered_cases = []

        # 失败原因过滤器，如果为None则包含所有失败用例
        self.filter_reasons = filter_reasons

        # 创建临时目录用于存放重测脚本
        self.temp_dir = self.project_root / "temp"
        self.temp_dir.mkdir(exist_ok=True)

        # 创建文档目录用于存放说明文档
        self.docs_dir = self.project_root / "docs"
        self.docs_dir.mkdir(exist_ok=True)

        log.info(f"失败用例批量重测工具初始化完成")
        log.info(f"Excel文件路径: {self.excel_path}")
        log.info(f"临时目录: {self.temp_dir}")
        log.info(f"文档目录: {self.docs_dir}")
        if self.filter_reasons:
            log.info(f"失败原因过滤器: {self.filter_reasons}")
        else:
            log.info("将重测所有失败用例")
    
    def _find_latest_failure_analysis_excel(self) -> str:
        """
        查找最新的失败分析Excel文件
        
        Returns:
            str: 最新Excel文件路径
        """
        failure_analysis_dir = self.project_root / "tools" / "allure_report_analysis" / "failure_analysis"
        
        if not failure_analysis_dir.exists():
            raise FileNotFoundError(f"失败分析目录不存在: {failure_analysis_dir}")
        
        # 查找所有failure_analysis_*.xlsx文件
        excel_files = list(failure_analysis_dir.glob("failure_analysis_*.xlsx"))
        
        if not excel_files:
            raise FileNotFoundError(f"未找到失败分析Excel文件: {failure_analysis_dir}")

        # 按文件名中的时间戳排序，返回最新的
        latest_file = max(excel_files, key=self._extract_timestamp_from_filename)
        return str(latest_file)
    def _extract_timestamp_from_filename(self, file_path: Path) -> datetime:
        """
        从文件名中提取时间戳

        Args:
            file_path: 文件路径

        Returns:
            datetime: 提取的时间戳，如果提取失败返回最小时间
        """
        import re
        from datetime import datetime

        try:
            # 文件名格式: failure_analysis_allure_report_25-09-03_13-26-24.xlsx
            # 提取时间戳部分: 25-09-03_13-26-24
            filename = file_path.name

            # 使用正则表达式提取时间戳
            # 匹配格式: YY-MM-DD_HH-MM-SS
            pattern = r'(\d{2})-(\d{2})-(\d{2})_(\d{2})-(\d{2})-(\d{2})'
            match = re.search(pattern, filename)

            if match:
                year, month, day, hour, minute, second = match.groups()

                # 将2位年份转换为4位年份（假设20xx年）
                full_year = 2000 + int(year)

                # 创建datetime对象
                timestamp = datetime(
                    year=full_year,
                    month=int(month),
                    day=int(day),
                    hour=int(hour),
                    minute=int(minute),
                    second=int(second)
                )

                log.debug(f"从文件名 {filename} 提取时间戳: {timestamp}")
                return timestamp
            else:
                log.warning(f"无法从文件名 {filename} 提取时间戳，使用文件修改时间")
                # 如果无法从文件名提取时间戳，使用文件修改时间
                return datetime.fromtimestamp(file_path.stat().st_mtime)

        except Exception as e:
            log.warning(f"提取时间戳时出错 {file_path.name}: {e}，使用最小时间")
            # 如果出错，返回最小时间，这样该文件会被排在最后
            return datetime.min
    def load_and_filter_excel(self) -> bool:
        """
        加载Excel文件并过滤失败用例

        Returns:
            bool: 是否成功加载和过滤
        """
        try:
            if not os.path.exists(self.excel_path):
                log.error(f"Excel文件不存在: {self.excel_path}")
                return False

            # 读取Excel文件
            df = pd.read_excel(self.excel_path)
            log.info(f"成功加载Excel文件，共 {len(df)} 条记录")

            # 首先过滤出失败的测试用例
            failed_df = df[df['测试状态'] == 'failed']
            log.info(f"找到 {len(failed_df)} 条失败记录")

            # 如果指定了失败原因过滤器，则进一步过滤
            if self.filter_reasons:
                filtered_df = failed_df[failed_df['失败原因'].isin(self.filter_reasons)]
                log.info(f"根据失败原因过滤后找到 {len(filtered_df)} 条符合条件的失败用例")
            else:
                filtered_df = failed_df
                log.info(f"将重测所有 {len(filtered_df)} 条失败用例")

            # 转换为字典列表
            self.filtered_cases = filtered_df.to_dict('records')

            # 打印统计信息
            self._print_filter_statistics()

            return True

        except Exception as e:
            log.error(f"加载和过滤Excel文件时出错: {e}")
            return False
    
    def _print_filter_statistics(self):
        """打印过滤统计信息"""
        if not self.filtered_cases:
            log.warning("没有找到符合条件的失败用例")
            return

        log.info("\n=== 过滤结果统计 ===")

        # 按失败原因统计
        reason_count = {}
        suite_count = {}
        parent_suite_count = {}

        for case in self.filtered_cases:
            reason = case.get('失败原因', '未知')
            suite = case.get('标签_suite', '未知')
            parent_suite = case.get('标签_parentSuite', '未知')

            reason_count[reason] = reason_count.get(reason, 0) + 1
            suite_count[suite] = suite_count.get(suite, 0) + 1
            parent_suite_count[parent_suite] = parent_suite_count.get(parent_suite, 0) + 1

        log.info("按失败原因统计:")
        for reason, count in sorted(reason_count.items(), key=lambda x: x[1], reverse=True):
            log.info(f"  {reason}: {count} 个")

        log.info("按测试模块统计:")
        for parent_suite, count in sorted(parent_suite_count.items(), key=lambda x: x[1], reverse=True)[:10]:
            log.info(f"  {parent_suite}: {count} 个")

        if len(parent_suite_count) > 10:
            log.info(f"  ... 还有 {len(parent_suite_count) - 10} 个模块")
    
    def generate_test_files_list(self) -> List[str]:
        """
        生成需要重测的测试文件列表
        
        Returns:
            List[str]: 测试文件路径列表
        """
        test_files = set()
        valid_files_count = 0
        invalid_files_count = 0
        
        for case in self.filtered_cases:
            suite = case.get('标签_suite', '')
            parent_suite = case.get('标签_parentSuite', '')
            
            if not suite or not parent_suite:
                invalid_files_count += 1
                continue
            
            # 根据规则生成测试文件路径
            test_file = self._generate_test_file_path(suite, parent_suite)
            
            if test_file:
                # 验证文件是否真实存在
                full_path = self.project_root / test_file
                if full_path.exists():
                    test_files.add(test_file)
                    valid_files_count += 1
                else:
                    log.warning(f"测试文件不存在，跳过: {test_file}")
                    invalid_files_count += 1
            else:
                invalid_files_count += 1
        
        log.info(f"文件验证结果: 有效文件 {valid_files_count} 个，无效文件 {invalid_files_count} 个")
        return sorted(list(test_files))
    
    def _generate_test_file_path(self, suite: str, parent_suite: str) -> str:
        """
        根据套件信息生成测试文件路径
        
        规则：
        1. 标签_parentSuite字段是脚本目录，例如：testcases.test_ella.system_coupling -> testcases/test_ella/system_coupling
        2. 标签_suite字段是脚本名称，例如：test_turn_off_adaptive_brightness
        3. 脚本完整路径：testcases/test_ella/system_coupling/test_turn_off_adaptive_brightness.py
        
        Args:
            suite: 测试套件名称（标签_suite字段）
            parent_suite: 父套件名称（标签_parentSuite字段）
            
        Returns:
            str: 测试文件路径，如果生成失败返回空字符串
        """
        try:
            # 确保suite以.py结尾
            if not suite.endswith('.py'):
                suite += '.py'
            
            # 将点号分隔的包路径转换为文件系统路径
            # 例如：testcases.test_ella.system_coupling -> testcases/test_ella/system_coupling
            directory_path = parent_suite.replace('.', '/')
            test_file_path = f"{directory_path}/{suite}"
            
            log.debug(f"生成测试文件路径: {test_file_path}")
            return test_file_path
            
        except Exception as e:
            log.warning(f"生成测试文件路径时出错: {e}")
            return ""
    
    def create_retest_script(self) -> str:
        """
        创建重测脚本

        Returns:
            str: 重测脚本路径
        """
        test_files = self.generate_test_files_list()

        if not test_files:
            log.error("没有找到有效的测试用例文件")
            return ""

        log.info(f"准备生成包含 {len(test_files)} 个有效测试文件的重测脚本")

        # 生成脚本内容
        script_content = self._generate_retest_script_content(test_files)

        # 保存脚本文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        script_path = self.temp_dir / f"failure_retest_{timestamp}.py"

        try:
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(script_content)

            log.info(f"重测脚本已生成: {script_path}")
            log.info(f"包含 {len(test_files)} 个有效测试文件")
            return str(script_path)

        except Exception as e:
            log.error(f"生成重测脚本时出错: {e}")
            return ""
    
    def _generate_retest_script_content(self, test_files: List[str]) -> str:
        """
        生成重测脚本内容

        Args:
            test_files: 测试文件列表

        Returns:
            str: 脚本内容
        """
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 生成过滤条件描述
        filter_desc = ""
        if self.filter_reasons:
            filter_desc = f"过滤条件: {', '.join(self.filter_reasons)}"
        else:
            filter_desc = "包含所有失败用例"

        script_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
失败用例批量重测脚本
自动生成时间: {timestamp}
{filter_desc}

本脚本基于失败分析Excel文件自动生成，用于重新测试失败的用例
共包含 {len(test_files)} 个测试文件
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.logger import log


class FailureRetestRunner:
    """失败用例批量重测执行器"""

    def __init__(self):
        self.project_root = project_root

        # 需要重测的测试文件列表
        self.test_files = [
'''
        
        # 添加测试文件列表
        for test_file in test_files:
            script_content += f'            "{test_file}",\n'
        
        script_content += '''        ]

    def run_retest(self):
        """执行重测"""
        log.info("开始执行失败用例批量重测")
        log.info(f"共需要重测 {len(self.test_files)} 个测试文件")

        # 固定的报告目录路径
        allure_results_dir = "reports/allure-results"
        allure_report_dir = "reports/allure-report"

        # 确保报告目录存在
        results_path = self.project_root / allure_results_dir
        report_path = self.project_root / allure_report_dir
        results_path.mkdir(parents=True, exist_ok=True)
        report_path.mkdir(parents=True, exist_ok=True)

        log.info(f"Allure测试数据将存储到: {allure_results_dir}")
        log.info(f"Allure报告将生成到: {allure_report_dir}")

        # 构建pytest命令
        cmd = [
            sys.executable, "-m", "pytest",
            *self.test_files,
            "-v",
            "--tb=short",
            f"--alluredir={allure_results_dir}",
            "--clean-alluredir"
        ]

        log.info(f"执行命令: {' '.join(cmd)}")

        try:
            # 执行pytest
            result = subprocess.run(cmd, cwd=self.project_root, capture_output=False)

            if result.returncode == 0:
                log.info("✅ 重测完成，所有测试通过")
            else:
                log.warning(f"⚠️ 重测完成，存在失败用例，退出码: {result.returncode}")

            # 生成Allure报告
            self._generate_allure_report(allure_results_dir, allure_report_dir)

            return result.returncode

        except Exception as e:
            log.error(f"执行重测时出错: {e}")
            return 1
    
    def _generate_allure_report(self, allure_results_dir: str, allure_report_dir: str):
        """生成Allure报告"""
        try:
            log.info(f"正在生成Allure报告...")
            log.info(f"数据源目录: {allure_results_dir}")
            log.info(f"报告输出目录: {allure_report_dir}")

            cmd = ["allure", "generate", allure_results_dir, "-o", allure_report_dir, "--clean"]
            result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)

            if result.returncode == 0:
                log.info(f"✅ Allure报告已生成: {allure_report_dir}")
                log.info(f"📊 可以通过以下命令查看报告: allure open {allure_report_dir}")

                # 提供完整的绝对路径信息
                full_report_path = self.project_root / allure_report_dir
                log.info(f"📁 报告完整路径: {full_report_path}")
            else:
                log.warning(f"⚠️ 生成Allure报告失败: {result.stderr}")

        except Exception as e:
            log.warning(f"生成Allure报告时出错: {e}")


def main():
    """主函数"""
    log.info("=== 失败用例批量重测工具 ===")

    runner = FailureRetestRunner()
    exit_code = runner.run_retest()

    log.info("=== 重测完成 ===")
    return exit_code


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
'''
        
        return script_content


    def create_documentation(self) -> str:
        """
        创建使用说明文档

        Returns:
            str: 文档路径
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        doc_path = self.docs_dir / f"failure_retest_guide_{timestamp}.md"

        # 统计信息
        total_cases = len(self.filtered_cases)
        test_files = self.generate_test_files_list()
        valid_files = len(test_files)

        # 生成文档内容
        doc_content = f"""# 失败用例批量重测工具使用说明

## 工具概述

本工具用于读取失败分析Excel文件，自动提取失败的测试用例，并生成pytest重测脚本。

## 生成时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 数据来源
- Excel文件: `{self.excel_path}`
- 总失败用例数: {total_cases}
- 有效测试文件数: {valid_files}

## 过滤条件
"""

        if self.filter_reasons:
            doc_content += f"- 失败原因过滤: {', '.join(self.filter_reasons)}\n"
        else:
            doc_content += "- 包含所有失败用例\n"

        doc_content += f"""
## 测试文件列表

以下是将要重测的测试文件:

"""

        for i, test_file in enumerate(test_files, 1):
            doc_content += f"{i}. `{test_file}`\n"

        doc_content += f"""
## 使用方法

### 1. 生成重测脚本
```bash
python tools/allure_report_analysis/command_support_retest_tool.py
```

### 2. 执行重测脚本
```bash
python temp/failure_retest_<timestamp>.py
```

### 3. 查看测试报告
重测完成后，会在 `reports/` 目录下生成Allure报告。

## 注意事项

1. 确保所有测试文件路径正确
2. 确保测试环境已正确配置
3. 建议在执行重测前备份当前代码状态
4. 重测可能需要较长时间，请耐心等待

## 路径转换规则

- 标签_parentSuite: `testcases.test_ella.system_coupling` → `testcases/test_ella/system_coupling`
- 标签_suite: `test_turn_off_adaptive_brightness` → `test_turn_off_adaptive_brightness.py`
- 完整路径: `testcases/test_ella/system_coupling/test_turn_off_adaptive_brightness.py`
"""

        try:
            with open(doc_path, 'w', encoding='utf-8') as f:
                f.write(doc_content)

            log.info(f"使用说明文档已生成: {doc_path}")
            return str(doc_path)

        except Exception as e:
            log.error(f"生成使用说明文档时出错: {e}")
            return ""


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="失败用例批量重测工具")
    parser.add_argument(
        "--excel-path",
        help="失败分析Excel文件路径，不指定则自动查找最新的"
    )
    parser.add_argument(
        "--filter-reasons",
        nargs="*",
        help="要过滤的失败原因列表，不指定则包含所有失败用例"
    )
    parser.add_argument(
        "--run-immediately",
        action="store_true",
        help="生成脚本后立即执行重测"
    )
    parser.add_argument(
        "--create-doc",
        action="store_true",
        help="生成使用说明文档"
    )

    args = parser.parse_args()

    try:
        # 创建重测工具
        tool = FailureRetestTool(args.excel_path, args.filter_reasons)

        # 加载和过滤Excel数据
        if not tool.load_and_filter_excel():
            log.error("加载Excel文件失败")
            return 1

        # 生成使用说明文档
        if args.create_doc:
            doc_path = tool.create_documentation()
            if doc_path:
                log.info(f"✅ 使用说明文档生成成功: {doc_path}")

        # 生成重测脚本
        script_path = tool.create_retest_script()
        if not script_path:
            log.error("生成重测脚本失败")
            return 1

        log.info(f"✅ 重测脚本生成成功: {script_path}")

        # 如果指定立即执行，则运行脚本
        if args.run_immediately:
            log.info("开始执行重测...")
            result = subprocess.run([sys.executable, script_path], cwd=tool.project_root)
            return result.returncode
        else:
            log.info(f"请手动执行重测脚本: python {script_path}")
            return 0

    except Exception as e:
        log.error(f"执行失败: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
